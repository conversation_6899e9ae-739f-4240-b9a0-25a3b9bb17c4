# Toggle do módulo Money
execute store result score #temp modmenu_money run scoreboard players get #money_enabled modmenu_money
execute if score #temp modmenu_money matches 0 run scoreboard players set #money_enabled modmenu_money 1
execute if score #temp modmenu_money matches 1 run scoreboard players set #money_enabled modmenu_money 0

execute if score #money_enabled modmenu_money matches 1 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo Money ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}]
execute if score #money_enabled modmenu_money matches 0 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo Money ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}]

# Reabrir menu
function modmenu:menu/main
