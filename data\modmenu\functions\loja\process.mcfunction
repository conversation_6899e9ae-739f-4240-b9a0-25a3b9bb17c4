# Processar interações com placas de loja
# Detectar clique direito próximo a placas
execute as @a[scores={loja_click=1..}] at @s run function modmenu:loja/detect_nearby_signs

# Processar compras
execute as @a[scores={buy_diamond=1..}] run function modmenu:loja/buy/diamond
execute as @a[scores={buy_bread=1..}] run function modmenu:loja/buy/bread
execute as @a[scores={buy_sword=1..}] run function modmenu:loja/buy/sword
execute as @a[scores={buy_armor=1..}] run function modmenu:loja/buy/armor

# Processar vendas
execute as @a[scores={sell_diamond=1..}] run function modmenu:loja/sell/diamond
execute as @a[scores={sell_gold=1..}] run function modmenu:loja/sell/gold
execute as @a[scores={sell_iron=1..}] run function modmenu:loja/sell/iron
execute as @a[scores={sell_emerald=1..}] run function modmenu:loja/sell/emerald
execute as @a[scores={sell_coal=1..}] run function modmenu:loja/sell/coal
