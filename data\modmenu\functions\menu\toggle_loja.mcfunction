# Toggle do módulo Loja
execute store result score #temp modmenu_loja run scoreboard players get #loja_enabled modmenu_loja
execute if score #temp modmenu_loja matches 0 run scoreboard players set #loja_enabled modmenu_loja 1
execute if score #temp modmenu_loja matches 1 run scoreboard players set #loja_enabled modmenu_loja 0

execute if score #loja_enabled modmenu_loja matches 1 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo Loja ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}]
execute if score #loja_enabled modmenu_loja matches 0 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo Loja ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}]

# Reabrir menu
function modmenu:menu/main
