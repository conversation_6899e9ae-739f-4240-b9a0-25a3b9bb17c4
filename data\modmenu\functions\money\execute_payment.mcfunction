# Executar pagamento
# Nota: Este é um sistema simplificado. Para um sistema completo de pagamento entre jogadores,
# seria necessário um sistema mais complexo para selecionar o destinatário.

scoreboard players operation @s money -= @s temp_pay

tellraw @s [{"text":"[Money] ","color":"green"},{"text":"Pagamento de ","color":"white"},{"score":{"name":"@s","objective":"temp_pay"},"color":"gold"},{"text":" moedas realizado!","color":"white"}]
tellraw @s [{"text":"[Money] ","color":"gold"},{"text":"Saldo atual: ","color":"white"},{"score":{"name":"@s","objective":"money"},"color":"green"},{"text":" moedas","color":"white"}]

playsound entity.experience_orb.pickup player @s ~ ~ ~ 1 1
