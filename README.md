# Mod Menu DataPack

Um sistema modular de datapack para Minecraft que permite ativar/desativar diferentes funcionalidades através de um menu interativo.

## Funcionalidades

### 🏠 Módulo Home
- `/trigger sethome` - Define sua casa na posição atual
- `/trigger home` - Teletransporta para sua casa

### 💰 Módulo Money
- Sistema de economia integrado
- `/trigger money_cmd` - Ver seu saldo
- `/trigger pay set <valor>` - Pagar outro jogador

### 🛒 Módulo Loja
- Sistema de compra/venda através de placas
- Crie placas com `[VENDA]` na primeira linha para vender itens
- Crie placas com `[COMPRA]` na primeira linha para comprar itens
- Clique direito na placa segurando o item para transacionar

### 🌍 Módulo TP
- `/trigger tpa set <id>` - Solicitar teletransporte para outro jogador
- `/trigger tpaccept` - Aceitar solicitação de TP
- `/trigger tpdeny` - Recusar solicitação de TP

## Como usar

1. **Instalar o DataPack**: Coloque a pasta na pasta `datapacks` do seu mundo
2. **Recarregar**: Use `/reload` no jogo
3. **Abrir Menu**: Use `/function modmenu:menu/main`
4. **Ativar Módulos**: Clique nos botões do menu para ativar/desativar módulos

## Comandos Administrativos

- `/function modmenu:admin/enable_all` - Ativa todos os módulos
- `/function modmenu:admin/reset` - Desativa todos os módulos
- `/function modmenu:menu/main` - Abre o menu principal

## Configuração de Placas de Loja

### Exemplo de Placa de Venda:
```
[VENDA]
Diamante
100 moedas
Clique aqui!
```

### Exemplo de Placa de Compra:
```
[COMPRA]
Diamante
80 moedas
Clique aqui!
```

## Notas Técnicas

- Compatible com Minecraft 1.20+
- Usa scoreboards para gerenciar estados
- Sistema modular permite ativação independente
- Triggers habilitados automaticamente para todos os jogadores

## Personalização

Para adicionar novos itens à loja, edite os arquivos:
- `data/modmenu/functions/loja/venda.mcfunction`
- `data/modmenu/functions/loja/compra.mcfunction`

Para modificar valores de economia, edite os arquivos correspondentes em:
- `data/modmenu/functions/loja/venda_*.mcfunction`
- `data/modmenu/functions/loja/compra_*.mcfunction`
