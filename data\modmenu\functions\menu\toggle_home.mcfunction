# Toggle do módulo Home
execute store result score #temp modmenu_home run scoreboard players get #home_enabled modmenu_home
execute if score #temp modmenu_home matches 0 run scoreboard players set #home_enabled modmenu_home 1
execute if score #temp modmenu_home matches 1 run scoreboard players set #home_enabled modmenu_home 0

execute if score #home_enabled modmenu_home matches 1 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo Home ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}]
execute if score #home_enabled modmenu_home matches 0 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo Home ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}]

# Reabrir menu
function modmenu:menu/main
