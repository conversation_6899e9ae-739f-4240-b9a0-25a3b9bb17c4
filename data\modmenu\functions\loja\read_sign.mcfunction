# Ler conteúdo da placa
# Verificar primeira linha para [VENDA] ou [COMPRA]
execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[VENDA]"}']}} run function modmenu:loja/venda
execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[COMPRA]"}']}} run function modmenu:loja/compra

# Verificar variações de texto
execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[venda]"}']}} run function modmenu:loja/venda
execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[compra]"}']}} run function modmenu:loja/compra
execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[Venda]"}']}} run function modmenu:loja/venda
execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[Compra]"}']}} run function modmenu:loja/compra
