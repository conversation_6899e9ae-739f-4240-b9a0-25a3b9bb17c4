# Detectar placas próximas
scoreboard players set @s loja_click 0

# Verificar placas em um raio de 3 blocos
execute if block ~1 ~ ~ #minecraft:signs run function modmenu:loja/check_sign_at {x:1,y:0,z:0}
execute if block ~-1 ~ ~ #minecraft:signs run function modmenu:loja/check_sign_at {x:-1,y:0,z:0}
execute if block ~ ~ ~1 #minecraft:signs run function modmenu:loja/check_sign_at {x:0,y:0,z:1}
execute if block ~ ~ ~-1 #minecraft:signs run function modmenu:loja/check_sign_at {x:0,y:0,z:-1}
execute if block ~ ~1 ~ #minecraft:signs run function modmenu:loja/check_sign_at {x:0,y:1,z:0}
execute if block ~ ~-1 ~ #minecraft:signs run function modmenu:loja/check_sign_at {x:0,y:-1,z:0}

# Verificar placas diagonais
execute if block ~1 ~ ~1 #minecraft:signs run function modmenu:loja/check_sign_at {x:1,y:0,z:1}
execute if block ~1 ~ ~-1 #minecraft:signs run function modmenu:loja/check_sign_at {x:1,y:0,z:-1}
execute if block ~-1 ~ ~1 #minecraft:signs run function modmenu:loja/check_sign_at {x:-1,y:0,z:1}
execute if block ~-1 ~ ~-1 #minecraft:signs run function modmenu:loja/check_sign_at {x:-1,y:0,z:-1}
