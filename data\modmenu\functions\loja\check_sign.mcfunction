# Verificar placas de loja
# Verificar se a placa contém [VENDA] ou [COMPRA]
execute if block ~ ~-1 ~ #minecraft:signs if data block ~ ~-1 ~ {front_text:{messages:['{"text":"[VENDA]"}']}} run function modmenu:loja/venda
execute if block ~ ~-1 ~ #minecraft:signs if data block ~ ~-1 ~ {front_text:{messages:['{"text":"[COMPRA]"}']}} run function modmenu:loja/compra

execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[VENDA]"}']}} run function modmenu:loja/venda
execute if block ~ ~ ~ #minecraft:signs if data block ~ ~ ~ {front_text:{messages:['{"text":"[COMPRA]"}']}} run function modmenu:loja/compra

execute if block ^ ^ ^1 #minecraft:signs if data block ^ ^ ^1 {front_text:{messages:['{"text":"[VENDA]"}']}} run function modmenu:loja/venda
execute if block ^ ^ ^1 #minecraft:signs if data block ^ ^ ^1 {front_text:{messages:['{"text":"[COMPRA]"}']}} run function modmenu:loja/compra
