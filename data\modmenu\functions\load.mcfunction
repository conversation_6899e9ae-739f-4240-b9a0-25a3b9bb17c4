# Inicialização do Mod Menu DataPack
tellraw @a {"text":"[ModMenu] DataPack carregado com sucesso!","color":"green"}

# Criar scoreboards para gerenciar módulos
scoreboard objectives add modmenu_home dummy "Home Module"
scoreboard objectives add modmenu_loja dummy "Loja Module"
scoreboard objectives add modmenu_money dummy "Money Module"
scoreboard objectives add modmenu_tp dummy "TP Module"
scoreboard objectives add money dummy "Dinheiro"
scoreboard objectives add home_set dummy "Home Definido"

# Inicializar estados dos módulos (0 = desativado, 1 = ativado)
scoreboard players set #home_enabled modmenu_home 0
scoreboard players set #loja_enabled modmenu_loja 0
scoreboard players set #money_enabled modmenu_money 0
scoreboard players set #tp_enabled modmenu_tp 0

# Dar dinheiro inicial para novos jogadores
execute as @a unless score @s money matches 0.. run scoreboard players set @s money 1000

# Definir valores constantes para a loja
scoreboard players set #50 modmenu_money 50
scoreboard players set #100 modmenu_money 100
scoreboard players set #150 modmenu_money 150
scoreboard players set #200 modmenu_money 200

# Inicializar módulos se estiverem ativados
execute if score #home_enabled modmenu_home matches 1 run function modmenu:home/init
execute if score #money_enabled modmenu_money matches 1 run function modmenu:money/init
execute if score #loja_enabled modmenu_loja matches 1 run function modmenu:loja/init
execute if score #tp_enabled modmenu_tp matches 1 run function modmenu:tp/init

tellraw @a [{"text":"[ModMenu] ","color":"gold"},{"text":"Use ","color":"yellow"},{"text":"/function modmenu:menu/main","color":"aqua","clickEvent":{"action":"run_command","value":"/function modmenu:menu/main"}},{"text":" para abrir o menu","color":"yellow"}]
