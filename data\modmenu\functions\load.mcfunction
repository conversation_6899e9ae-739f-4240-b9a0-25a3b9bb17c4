# Inicialização do Mod Menu DataPack
tellraw @a {"text":"[ModMenu] DataPack carregado com sucesso!","color":"green"}

# Criar scoreboards para gerenciar módulos
scoreboard objectives add modmenu_home dummy "Home Module"
scoreboard objectives add modmenu_loja dummy "Loja Module"
scoreboard objectives add modmenu_money dummy "Money Module"
scoreboard objectives add modmenu_tp dummy "TP Module"
scoreboard objectives add money dummy "Dinheiro"
scoreboard objectives add home_set dummy "Home Definido"

# Inicializar estados dos módulos (0 = desativado, 1 = ativado)
scoreboard players set #home_enabled modmenu_home 0
scoreboard players set #loja_enabled modmenu_loja 0
scoreboard players set #money_enabled modmenu_money 0
scoreboard players set #tp_enabled modmenu_tp 0

# Dar dinheiro inicial para novos jogadores
execute as @a unless score @s money matches 0.. run scoreboard players set @s money 1000

tellraw @a [{"text":"[ModMenu] ","color":"gold"},{"text":"Use ","color":"yellow"},{"text":"/function modmenu:menu/main","color":"aqua","clickEvent":{"action":"run_command","value":"/function modmenu:menu/main"}},{"text":" para abrir o menu","color":"yellow"}]
