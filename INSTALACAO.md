# 🎮 Instalação e Uso do Mod Menu DataPack

## 📋 Pré-requisitos
- Minecraft Java Edition 1.20 ou superior
- Mundo em modo Criativo ou com permissões de administrador

## 🔧 Instalação

### Passo 1: Baixar o DataPack
1. Baixe todos os arquivos deste projeto
2. Mantenha a estrutura de pastas intacta

### Passo 2: Instalar no Mundo
1. Abra a pasta do seu mundo Minecraft
2. Navegue até a pasta `datapacks`
3. Crie uma nova pasta chamada `modmenu`
4. Copie todos os arquivos do projeto para dentro desta pasta

### Passo 3: Ativar no Jogo
1. Entre no mundo
2. Execute o comando: `/reload`
3. Você verá a mensagem de confirmação do carregamento

## 🚀 Primeiro Uso

### Abrir o Menu Principal
```
/function modmenu:menu/main
```

### Ativar Todos os Módulos (Recomendado)
```
/function modmenu:admin/enable_all
```

## 🏠 Módulo Home

### Ativar
- Clique em "Home" no menu principal para ativar/desativar

### Comandos
- `/trigger sethome` - Define sua casa na posição atual
- `/trigger home` - Teletransporta para sua casa

## 💰 Módulo Money

### Ativar
- Clique em "Money" no menu principal para ativar/desativar

### Comandos
- `/trigger money_cmd` - Ver seu saldo atual
- `/trigger pay set <valor>` - Sistema de pagamento (simplificado)

### Saldo Inicial
- Todos os jogadores começam com 1000 moedas

## 🛒 Módulo Loja

### Ativar
- Clique em "Loja" no menu principal para ativar/desativar

### Como Usar Placas de Loja

#### Criar Placa de Venda (Loja vende para jogador)
1. Coloque uma placa
2. Na primeira linha escreva: `[VENDA]`
3. Clique direito na placa para ver itens disponíveis

#### Criar Placa de Compra (Loja compra do jogador)
1. Coloque uma placa
2. Na primeira linha escreva: `[COMPRA]`
3. Clique direito na placa para vender seus itens

### Itens e Preços

#### Loja Vende:
- 💎 Diamante: 100 moedas
- 🥖 Pão x10: 50 moedas
- ⚔️ Espada de Ferro Encantada: 200 moedas
- 🛡️ Conjunto de Armadura de Ferro: 150 moedas

#### Loja Compra:
- 💎 Diamante: 80 moedas cada
- 🥇 Ouro: 30 moedas cada
- 🔩 Ferro: 15 moedas cada
- 💚 Esmeralda: 60 moedas cada
- ⚫ Carvão: 5 moedas cada

## 🌍 Módulo TP

### Ativar
- Clique em "TP" no menu principal para ativar/desativar

### Comandos
- `/trigger tpa set <id>` - Solicitar teletransporte (sistema simplificado)
- `/trigger tpaccept` - Aceitar solicitação de TP
- `/trigger tpdeny` - Recusar solicitação de TP

### Tempo Limite
- Solicitações de TP expiram em 30 segundos (600 ticks)

## ⚙️ Comandos Administrativos

### Menu e Controle
- `/function modmenu:menu/main` - Abrir menu principal
- `/function modmenu:menu/help` - Ver comandos disponíveis

### Gerenciamento de Módulos
- `/function modmenu:admin/enable_all` - Ativar todos os módulos
- `/function modmenu:admin/reset` - Desativar todos os módulos

### Módulos Individuais
- `/function modmenu:menu/toggle_home` - Toggle módulo Home
- `/function modmenu:menu/toggle_money` - Toggle módulo Money
- `/function modmenu:menu/toggle_loja` - Toggle módulo Loja
- `/function modmenu:menu/toggle_tp` - Toggle módulo TP

## 🔧 Solução de Problemas

### DataPack não carrega
1. Verifique se a estrutura de pastas está correta
2. Execute `/reload` no jogo
3. Verifique se você tem permissões de administrador

### Comandos não funcionam
1. Certifique-se de que o módulo está ativado
2. Use `/function modmenu:admin/enable_all` para ativar tudo
3. Execute `/reload` se necessário

### Placas de loja não respondem
1. Verifique se o módulo Loja está ativado
2. Certifique-se de que a primeira linha da placa é exatamente `[VENDA]` ou `[COMPRA]`
3. Clique direito próximo à placa

## 📝 Notas Importantes

- O sistema de TP é simplificado e pode precisar de melhorias para uso em servidores
- Os valores da loja podem ser ajustados editando os arquivos correspondentes
- Todos os dados são salvos automaticamente com o mundo
- O sistema é compatível com outros datapacks
