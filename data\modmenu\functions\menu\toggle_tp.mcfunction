# Toggle do módulo TP
execute store result score #temp modmenu_tp run scoreboard players get #tp_enabled modmenu_tp
execute if score #temp modmenu_tp matches 0 run scoreboard players set #tp_enabled modmenu_tp 1
execute if score #temp modmenu_tp matches 1 run scoreboard players set #tp_enabled modmenu_tp 0

execute if score #tp_enabled modmenu_tp matches 1 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo TP ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}]
execute if score #tp_enabled modmenu_tp matches 0 run tellraw @s [{"text":"[ModMenu] ","color":"gold"},{"text":"Módulo TP ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}]

# Reabrir menu
function modmenu:menu/main
