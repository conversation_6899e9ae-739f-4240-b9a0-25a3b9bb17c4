# Definir Home
execute store result score @s home_x run data get entity @s Pos[0]
execute store result score @s home_y run data get entity @s Pos[1]
execute store result score @s home_z run data get entity @s Pos[2]

scoreboard players set @s home_set 1
scoreboard players set @s sethome 0

tellraw @s [{"text":"[Home] ","color":"green"},{"text":"Home definido na posição atual!","color":"white"}]
playsound entity.experience_orb.pickup player @s ~ ~ ~ 1 1
