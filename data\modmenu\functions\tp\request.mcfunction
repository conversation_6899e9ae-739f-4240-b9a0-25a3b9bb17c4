# Solicitar TP
# Nota: Sistema simplificado. Para um sistema completo, seria necessário
# um sistema mais complexo para identificar o jogador alvo.

tellraw @s [{"text":"[TP] ","color":"blue"},{"text":"Solicitação de TP enviada!","color":"white"}]
tellraw @a[distance=0.1..] [{"text":"[TP] ","color":"blue"},{"selector":"@s","color":"yellow"},{"text":" quer se teletransportar até você!","color":"white"}]
tellraw @a[distance=0.1..] [{"text":"[TP] ","color":"blue"},{"text":"[ACEITAR]","color":"green","clickEvent":{"action":"run_command","value":"/trigger tpaccept"}},{"text":" | ","color":"gray"},{"text":"[RECUSAR]","color":"red","clickEvent":{"action":"run_command","value":"/trigger tpdeny"}}]

scoreboard players operation @a[distance=0.1..] tp_request = @s tpa
scoreboard players set @a[distance=0.1..] tp_timer 600

scoreboard players set @s tpa 0
