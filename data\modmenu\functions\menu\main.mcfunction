# Menu Principal do Mod Menu
tellraw @s {"text":"","extra":[{"text":"=== ","color":"gold"},{"text":"MOD MENU","color":"yellow","bold":true},{"text":" ===","color":"gold"}]}
tellraw @s {"text":""}

# Status dos módulos
execute if score #home_enabled modmenu_home matches 1 run tellraw @s [{"text":"🏠 Home: ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}," ",{"text":"[DESATIVAR]","color":"red","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_home"}}]
execute if score #home_enabled modmenu_home matches 0 run tellraw @s [{"text":"🏠 Home: ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}," ",{"text":"[ATIVAR]","color":"green","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_home"}}]

execute if score #loja_enabled modmenu_loja matches 1 run tellraw @s [{"text":"🛒 Loja: ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}," ",{"text":"[DESATIVAR]","color":"red","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_loja"}}]
execute if score #loja_enabled modmenu_loja matches 0 run tellraw @s [{"text":"🛒 Loja: ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}," ",{"text":"[ATIVAR]","color":"green","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_loja"}}]

execute if score #money_enabled modmenu_money matches 1 run tellraw @s [{"text":"💰 Money: ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}," ",{"text":"[DESATIVAR]","color":"red","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_money"}}]
execute if score #money_enabled modmenu_money matches 0 run tellraw @s [{"text":"💰 Money: ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}," ",{"text":"[ATIVAR]","color":"green","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_money"}}]

execute if score #tp_enabled modmenu_tp matches 1 run tellraw @s [{"text":"🌍 TP: ","color":"white"},{"text":"ATIVADO","color":"green","bold":true}," ",{"text":"[DESATIVAR]","color":"red","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_tp"}}]
execute if score #tp_enabled modmenu_tp matches 0 run tellraw @s [{"text":"🌍 TP: ","color":"white"},{"text":"DESATIVADO","color":"red","bold":true}," ",{"text":"[ATIVAR]","color":"green","clickEvent":{"action":"run_command","value":"/function modmenu:menu/toggle_tp"}}]

tellraw @s {"text":""}
tellraw @s [{"text":"💰 Seu dinheiro: ","color":"yellow"},{"score":{"name":"@s","objective":"money"},"color":"gold"},{"text":" moedas","color":"yellow"}]
tellraw @s {"text":""}
tellraw @s [{"text":"📋 ","color":"blue"},{"text":"[COMANDOS DISPONÍVEIS]","color":"aqua","clickEvent":{"action":"run_command","value":"/function modmenu:menu/help"}}]
tellraw @s {"text":"================================","color":"gold"}
