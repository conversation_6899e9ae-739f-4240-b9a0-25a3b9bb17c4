# Menu de Ajuda
tellraw @s {"text":"","extra":[{"text":"=== ","color":"gold"},{"text":"COMANDOS DISPONÍVEIS","color":"yellow","bold":true},{"text":" ===","color":"gold"}]}
tellraw @s {"text":""}

# Comandos do Home
execute if score #home_enabled modmenu_home matches 1 run tellraw @s [{"text":"🏠 HOME:","color":"green","bold":true}]
execute if score #home_enabled modmenu_home matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"/trigger sethome","color":"aqua","clickEvent":{"action":"suggest_command","value":"/trigger sethome"}},{"text":" - Definir home","color":"white"}]
execute if score #home_enabled modmenu_home matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"/trigger home","color":"aqua","clickEvent":{"action":"suggest_command","value":"/trigger home"}},{"text":" - Ir para home","color":"white"}]

# Comandos do Money
execute if score #money_enabled modmenu_money matches 1 run tellraw @s [{"text":"💰 MONEY:","color":"green","bold":true}]
execute if score #money_enabled modmenu_money matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"/trigger money_cmd","color":"aqua","clickEvent":{"action":"suggest_command","value":"/trigger money_cmd"}},{"text":" - Ver saldo","color":"white"}]
execute if score #money_enabled modmenu_money matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"/trigger pay set <valor>","color":"aqua","clickEvent":{"action":"suggest_command","value":"/trigger pay set "}},{"text":" - Pagar jogador","color":"white"}]

# Comandos do TP
execute if score #tp_enabled modmenu_tp matches 1 run tellraw @s [{"text":"🌍 TELETRANSPORTE:","color":"green","bold":true}]
execute if score #tp_enabled modmenu_tp matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"/trigger tpa set <id>","color":"aqua","clickEvent":{"action":"suggest_command","value":"/trigger tpa set "}},{"text":" - Solicitar TP","color":"white"}]
execute if score #tp_enabled modmenu_tp matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"/trigger tpaccept","color":"aqua","clickEvent":{"action":"suggest_command","value":"/trigger tpaccept"}},{"text":" - Aceitar TP","color":"white"}]
execute if score #tp_enabled modmenu_tp matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"/trigger tpdeny","color":"aqua","clickEvent":{"action":"suggest_command","value":"/trigger tpdeny"}},{"text":" - Recusar TP","color":"white"}]

# Comandos da Loja
execute if score #loja_enabled modmenu_loja matches 1 run tellraw @s [{"text":"🛒 LOJA:","color":"green","bold":true}]
execute if score #loja_enabled modmenu_loja matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"Clique direito em placas de loja","color":"white"}]
execute if score #loja_enabled modmenu_loja matches 1 run tellraw @s [{"text":"  • ","color":"gray"},{"text":"Formato: [VENDA] ou [COMPRA]","color":"white"}]

tellraw @s {"text":""}
tellraw @s [{"text":"📋 ","color":"blue"},{"text":"[VOLTAR AO MENU]","color":"aqua","clickEvent":{"action":"run_command","value":"/function modmenu:menu/main"}}]
tellraw @s {"text":"================================","color":"gold"}
