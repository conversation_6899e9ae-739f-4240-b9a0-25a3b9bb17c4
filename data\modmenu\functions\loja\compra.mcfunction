# Sistema de compra - Loja compra do jogador
tellraw @s [{"text":"[Loja] ","color":"blue"},{"text":"=== VENDEMOS SEUS ITENS ===","color":"yellow"}]

# Verificar itens no inventário e mostrar preços
execute if entity @s[nbt={Inventory:[{id:"minecraft:diamond"}]}] run tellraw @s [{"text":"💎 ","color":"aqua"},{"text":"Diamante - 80 moedas cada ","color":"white"},{"text":"[VENDER]","color":"green","clickEvent":{"action":"run_command","value":"/trigger sell_diamond"}}]

execute if entity @s[nbt={Inventory:[{id:"minecraft:gold_ingot"}]}] run tellraw @s [{"text":"🥇 ","color":"yellow"},{"text":"Ouro - 30 moedas cada ","color":"white"},{"text":"[VENDER]","color":"green","clickEvent":{"action":"run_command","value":"/trigger sell_gold"}}]

execute if entity @s[nbt={Inventory:[{id:"minecraft:iron_ingot"}]}] run tellraw @s [{"text":"🔩 ","color":"gray"},{"text":"Ferro - 15 moedas cada ","color":"white"},{"text":"[VENDER]","color":"green","clickEvent":{"action":"run_command","value":"/trigger sell_iron"}}]

execute if entity @s[nbt={Inventory:[{id:"minecraft:emerald"}]}] run tellraw @s [{"text":"💚 ","color":"green"},{"text":"Esmeralda - 60 moedas cada ","color":"white"},{"text":"[VENDER]","color":"green","clickEvent":{"action":"run_command","value":"/trigger sell_emerald"}}]

execute if entity @s[nbt={Inventory:[{id:"minecraft:coal"}]}] run tellraw @s [{"text":"⚫ ","color":"dark_gray"},{"text":"Carvão - 5 moedas cada ","color":"white"},{"text":"[VENDER]","color":"green","clickEvent":{"action":"run_command","value":"/trigger sell_coal"}}]

tellraw @s [{"text":"💰 ","color":"gold"},{"text":"Seu saldo: ","color":"yellow"},{"score":{"name":"@s","objective":"money"},"color":"green"},{"text":" moedas","color":"yellow"}]
tellraw @s [{"text":"ℹ️ ","color":"blue"},{"text":"Segure o item que deseja vender e clique em [VENDER]","color":"gray"}]
